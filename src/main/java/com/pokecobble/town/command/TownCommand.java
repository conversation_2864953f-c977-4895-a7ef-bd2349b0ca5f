package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.client.InviteNotification;
import com.pokecobble.town.command.BackupCommand;
import com.pokecobble.town.command.TownElectionCommand;
import com.pokecobble.town.command.TownAdminCommand;
import com.pokecobble.town.command.ChunkRenderCommand;
import com.pokecobble.town.gui.ModernTownScreen;
import com.pokecobble.town.gui.TownScreenManager;
import com.pokecobble.phone.notification.PhoneNotificationManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandRegistrationCallback;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.sound.PositionedSoundInstance;
import net.minecraft.sound.SoundEvents;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.text.Text;

import java.util.Random;
import java.util.UUID;

import static net.minecraft.server.command.CommandManager.literal;

/**
 * Registers and handles town-related commands.
 */
public class TownCommand {

    /**
     * Registers server-side town commands.
     */
    public static void registerServerCommands() {
        CommandRegistrationCallback.EVENT.register(TownCommand::register);
    }

    /**
     * Registers client-side town commands.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientCommands() {
        ClientCommandRegistrationCallback.EVENT.register(TownCommand::registerClient);
    }

    /**
     * Registers server-side town commands.
     *
     * @param dispatcher The command dispatcher
     * @param registryAccess The command registry access
     * @param environment The registration environment
     */
    private static void register(CommandDispatcher<ServerCommandSource> dispatcher,
                                CommandRegistryAccess registryAccess,
                                CommandManager.RegistrationEnvironment environment) {

        Pokecobbleclaim.LOGGER.info("Registering server-side town commands");

        // Register /town command
        dispatcher.register(literal("town")
                .executes(context -> {
                    // This will be executed on the server side
                    // We'll send a message to the client to open the screen
                    Pokecobbleclaim.LOGGER.info("Server command /town executed");
                    context.getSource().sendFeedback(() -> Text.literal("Opening town interface..."), false);

                    // The actual screen opening happens on the client side via the CommandMixin
                    return 1;
                }));

        // Register /ville command (alias for /town)
        dispatcher.register(literal("ville")
                .executes(context -> {
                    // This will be executed on the server side
                    Pokecobbleclaim.LOGGER.info("Server command /ville executed");
                    context.getSource().sendFeedback(() -> Text.literal("Opening town interface..."), false);

                    // The actual screen opening happens on the client side via the CommandMixin
                    return 1;
                }));

        // Register the town election command
        TownElectionCommand.register(dispatcher, registryAccess, environment);

        // Register the town admin commands
        TownAdminCommand.register(dispatcher, registryAccess, environment);

        // Register the backup command
        BackupCommand.register(dispatcher, registryAccess, environment);

        Pokecobbleclaim.LOGGER.info("Server-side town commands registered successfully");
    }

    /**
     * Registers client-side town commands.
     *
     * @param dispatcher The client command dispatcher
     * @param registryAccess The command registry access
     */
    @Environment(EnvType.CLIENT)
    private static void registerClient(CommandDispatcher<FabricClientCommandSource> dispatcher,
                                      CommandRegistryAccess registryAccess) {

        Pokecobbleclaim.LOGGER.info("Registering client-side town commands");

        // Register /town command
        dispatcher.register(ClientCommandManager.literal("town")
                .executes(context -> {
                    Pokecobbleclaim.LOGGER.info("Client command /town executed");
                    // Open the town screen on the client side
                    TownScreenManager.openTownScreen();
                    return 1;
                }));

        // Register /towndev testnotification command
        dispatcher.register(ClientCommandManager.literal("towndev")
                .then(ClientCommandManager.literal("testnotification")
                        .then(ClientCommandManager.argument("townName", StringArgumentType.greedyString())
                                .executes(context -> {
                                    String townName = StringArgumentType.getString(context, "townName");
                                    Pokecobbleclaim.LOGGER.info("Testing town invitation notification for town: " + townName);

                                    // Create a dummy town with the specified name
                                    UUID townId = java.util.UUID.randomUUID();
                                    Town dummyTown = createDummyTown(townId, townName);

                                    // Add the dummy town to TownManager for testing
                                    TownManager.getInstance().addTown(dummyTown);

                                    // Set a test invitation notification (for legacy compatibility)
                                    InviteNotification.setPendingInvite(townId, townName);

                                    // Add phone notification (this is the new system)
                                    PhoneNotificationManager.getInstance().addTownInviteNotification(townName, townId.toString());

                                    // Send feedback to the player
                                    context.getSource().sendFeedback(Text.literal("Test invitation notification created for town: " + townName));
                                    return 1;
                                })
                        )
                )
        );

        // Register /testphone command
        dispatcher.register(ClientCommandManager.literal("testphone")
                .then(ClientCommandManager.argument("townName", StringArgumentType.greedyString())
                        .executes(context -> {
                            String townName = StringArgumentType.getString(context, "townName");
                            Pokecobbleclaim.LOGGER.info("Testing phone notification for town: " + townName);

                            // Create a dummy town ID
                            UUID townId = java.util.UUID.randomUUID();

                            // Add phone notification directly
                            PhoneNotificationManager.getInstance().addTownInviteNotification(townName, townId.toString());

                            // Send feedback to the player
                            context.getSource().sendFeedback(Text.literal("Test phone notification created for town: " + townName));
                            return 1;
                        })
                )
        );

        // Register /testoverlay command for testing notification overlay
        dispatcher.register(ClientCommandManager.literal("testoverlay")
                .executes(context -> {
                    Pokecobbleclaim.LOGGER.info("Testing notification overlay");

                    // Force show the overlay for testing
                    com.pokecobble.phone.notification.PhoneNotificationOverlay.getInstance().forceShowOverlay();
                    com.pokecobble.phone.gui.PhoneRenderer.getInstance().setVisible(true);

                    // Send feedback to the player
                    context.getSource().sendFeedback(Text.literal("Notification overlay test activated - press F to toggle"));
                    return 1;
                })
        );

        // Register /ville command (alias for /town)
        dispatcher.register(ClientCommandManager.literal("ville")
                .executes(context -> {
                    Pokecobbleclaim.LOGGER.info("Client command /ville executed");
                    // Open the town screen on the client side
                    TownScreenManager.openTownScreen();
                    return 1;
                }));

        // We don't register the election command on the client side
        // It's handled by the server

        // Register the chunk render command
        ChunkRenderCommand.register();

        // Register /testtownsettings command for testing town settings functionality
        dispatcher.register(ClientCommandManager.literal("testtownsettings")
                .executes(context -> {
                    Pokecobbleclaim.LOGGER.info("Testing town settings functionality");

                    // Run the town settings test
                    com.pokecobble.town.test.TownSettingsTest.testTownSettings();

                    // Send feedback to the player
                    context.getSource().sendFeedback(Text.literal("Town settings test executed - check console for results"));
                    return 1;
                })
        );

        // Register /opentownsettings command for testing the town settings screen
        dispatcher.register(ClientCommandManager.literal("opentownsettings")
                .executes(context -> {
                    MinecraftClient client = MinecraftClient.getInstance();
                    if (client.player == null) {
                        context.getSource().sendFeedback(Text.literal("No player found"));
                        return 0;
                    }

                    // Get the player's town
                    com.pokecobble.town.Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(client.player.getUuid());
                    if (playerTown == null) {
                        context.getSource().sendFeedback(Text.literal("You are not in a town!"));
                        return 0;
                    }

                    // Open the town settings screen
                    client.setScreen(new com.pokecobble.town.gui.TownSettingsScreen(null, playerTown));

                    return 1;
                })
        );

        Pokecobbleclaim.LOGGER.info("Client-side town commands registered successfully");
    }

    /**
     * Creates a dummy town for testing purposes.
     *
     * @param townId The ID to use for the town
     * @param townName The name of the town
     * @return A dummy town with random properties
     */
    private static Town createDummyTown(UUID townId, String townName) {
        Random random = new Random();

        // Create descriptions based on town name
        String[] descriptionTemplates = {
            "A beautiful town nestled in the hills of %s",
            "The bustling community of %s welcomes you",
            "Founded by ancient builders, %s stands proud",
            "A peaceful haven for all who visit %s",
            "The legendary town of %s awaits your arrival"
        };

        // Select a random description template
        String descriptionTemplate = descriptionTemplates[random.nextInt(descriptionTemplates.length)];
        String description = String.format(descriptionTemplate, townName);

        // Determine join type (33% chance for each type)
        Town.JoinType joinType;
        int joinTypeRoll = random.nextInt(3);
        switch (joinTypeRoll) {
            case 0:
                joinType = Town.JoinType.OPEN;
                break;
            case 1:
                joinType = Town.JoinType.CLOSED;
                break;
            default:
                joinType = Town.JoinType.INVITE_ONLY;
                break;
        }

        // Random max players between 10 and 50
        int maxPlayers = 10 + random.nextInt(41);

        // Create the town
        Town town = new Town(townId, townName);
        town.setDescription(description);
        town.setJoinType(joinType);
        town.setMaxPlayers(maxPlayers);

        // Add some random players (between 3 and 10)
        int playerCount = 3 + random.nextInt(8);
        String[] playerNamePrefixes = {"Builder", "Miner", "Crafter", "Explorer", "Warrior", "Farmer", "Hunter", "Wizard"};
        String[] playerNameSuffixes = {"Steve", "Alex", "Notch", "Jeb", "Dinnerbone", "Grumm", "Marc", "Searge"};

        for (int i = 0; i < playerCount; i++) {
            // Generate a random player name
            String prefix = playerNamePrefixes[random.nextInt(playerNamePrefixes.length)];
            String suffix = playerNameSuffixes[random.nextInt(playerNameSuffixes.length)];
            String playerName = prefix + suffix + random.nextInt(100);

            // Add the player to the town
            UUID playerId = UUID.randomUUID();
            town.addPlayer(playerId);

            // Assign a random rank (weighted towards regular members)
            int rankRoll = random.nextInt(100);
            TownPlayerRank rank;

            if (i == 0) {
                // First player is always the owner/mayor
                rank = TownPlayerRank.OWNER;
            } else if (rankRoll < 10) {
                // 10% chance for admin
                rank = TownPlayerRank.ADMIN;
            } else if (rankRoll < 25) {
                // 15% chance for moderator
                rank = TownPlayerRank.MODERATOR;
            } else if (rankRoll < 90) {
                // 65% chance for member
                rank = TownPlayerRank.MEMBER;
            } else {
                // 10% chance for visitor
                rank = TownPlayerRank.VISITOR;
            }

            town.setPlayerRank(playerId, rank);
        }

        // Set a random claim count between 1 and 20
        town.setClaimCount(1 + random.nextInt(20));

        return town;
    }
}
